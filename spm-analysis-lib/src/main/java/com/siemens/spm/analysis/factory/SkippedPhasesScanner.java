package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.Interval;
import com.siemens.spm.analysis.factory.splitmonitor.SplitMonitorChartBuilder;
import com.siemens.spm.analysis.vo.splitmonitor.SkippedInterval;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PatternInfoVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class SkippedPhasesScanner {

    /**
     * Scan for skipped phase intervals. A phase is considered skipped if it meets one of the following conditions:
     * <ul>
     * <li> Configured as omitted in intersection configuration
     * <li> Skipped during non-free plan execution (by comparing with the ring pattern)
     * <li> Marked as skipped during free plan execution (due to excessive green time)
     * </ul> 
     * Consecutive intervals of the same phase in the same plan are concatenated.
     *
     * @return Map of phase number to list of skipped intervals.
     * @see SkippedInterval#concatenateSkippedIntervals(LinkedList)
     */
    public static Map<Integer, LinkedList<SkippedInterval>> scan(
            PerfLogBundleVO perfLogBundleVO,
            Long initialPattern,
            Set<Integer> phaseSet,
            List<Pair<LocalDateTime, LocalDateTime>> gapPeriods) {
        if (perfLogBundleVO == null) {
            return Map.of();
        }

        List<PerfLogChunkVO> perfLogChunkVOList = perfLogBundleVO.getPerfLogChunks();
        Map<String, IntersectionConfigVO> intConfigMap = perfLogBundleVO.getIntConfigs();
        if (perfLogChunkVOList == null || intConfigMap == null) {
            return Map.of();
        }

        Map<Integer, LinkedList<SkippedInterval>> omittedPhaseIntervals = Map.of();
        
        try {
            omittedPhaseIntervals = scan(perfLogBundleVO, phaseSet);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        try {
            SplitMonitorChartVO chartVO = new SplitMonitorChartVO();
            chartVO.setFromTime(perfLogBundleVO.getFromTime());
            chartVO.setToTime(perfLogBundleVO.getToTime());

            SplitMonitorChartBuilder builder = new SplitMonitorChartBuilder(chartVO, initialPattern, gapPeriods);
            for (PerfLogChunkVO chunkVO : perfLogChunkVOList) {
                IntersectionConfigVO intConfigVO = intConfigMap.get(chunkVO.getConfigID());
                if (intConfigVO == null) {
                    continue;
                }

                builder.setIntersectionConfig(intConfigVO);
                chunkVO.getPerfLogEvents().forEach(builder::putEvent);
            }

            Map<Integer, LinkedList<SkippedInterval>> runtimeSkippedIntervalsMap = builder.skippedPhaseIntervals();

            if (omittedPhaseIntervals.isEmpty()) {
                return runtimeSkippedIntervalsMap;
            }

            return combineSkippedIntervalsMap(phaseSet, omittedPhaseIntervals, runtimeSkippedIntervalsMap);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return Map.of();
        }
    }

    /**
     * Scan for omitted phases in the intersection configurations (split mode = 7)
     * A skipped interval is then defined by [max(perfLog_fromTime, config_validFromTime), min(perfLog_toTime, config_validToTime)].
     * 
     * @param perfLogBundleVO The performance log bundle containing intersection configurations.
     * @param phaseSet The set of phases defined in the intersection configuration.
     * @return Map of phase number to list of skipped intervals.
     */
    private static Map<Integer, LinkedList<SkippedInterval>> scan(
            PerfLogBundleVO perfLogBundleVO, 
            Set<Integer> phaseSet) {
        if (phaseSet == null || phaseSet.isEmpty()) {
            return Map.of();
        }

        Map<Integer, LinkedList<SkippedInterval>> skippedIntervalsMap = new HashMap<>();
        List<IntersectionConfigVO> intConfigVOList = perfLogBundleVO.getIntConfigs().values().stream()
                .filter(intConfigVO -> intConfigVO.getPatternInfo() != null 
                                        && intConfigVO.getPatternInfo().getSplitMode() != null
                                        && intConfigVO.getValidFromTime() != null
                                        && intConfigVO.getValidToTime() != null)
                .sorted(Comparator.comparing(IntersectionConfigVO::getValidFromTime))
                .toList();

        LocalDateTime from = perfLogBundleVO.getFromTime();
        LocalDateTime to = perfLogBundleVO.getToTime();

        for (IntersectionConfigVO intConfigVO : intConfigVOList) {
            PatternInfoVO patternInfo = intConfigVO.getPatternInfo();

            for (int patternIdx = 0; patternIdx < patternInfo.getSplitMode().length; ++patternIdx) {
                int[] splitMode = patternInfo.getSplitMode()[patternIdx];
                if (splitMode == null || splitMode.length == 0) {
                    continue;
                }

                for (Integer phase : phaseSet) {
                    if (phase == null || phase < 1 || phase > splitMode.length) {
                        continue;
                    }
                    if (splitMode[phase - 1] == 7) {
                        LocalDateTime fromTime = Interval.max(from, intConfigVO.getValidFromTime());
                        LocalDateTime toTime = Interval.min(to, intConfigVO.getValidToTime());
                        SkippedInterval skippedInterval = new SkippedInterval(
                                patternIdx + 1, fromTime, toTime,
                                SkippedInterval.SkippedIntervalReason.OMITTED_PHASE);

                        skippedIntervalsMap
                                .computeIfAbsent(phase, k -> new LinkedList<>())
                                .add(skippedInterval);
                    }
                }
            }
        }

        return skippedIntervalsMap;
    }

    private static Map<Integer, LinkedList<SkippedInterval>> combineSkippedIntervalsMap(
            Set<Integer> phaseSet,
            Map<Integer, LinkedList<SkippedInterval>> omittedPhaseIntervals,
            Map<Integer, LinkedList<SkippedInterval>> runtimeSkippedIntervalsMap) {
        Map<Integer, LinkedList<SkippedInterval>> skippedIntervalsMap = new HashMap<>();

        for (Integer phase : phaseSet) {
            if (phase == null) {
                continue;
            }

            LinkedList<SkippedInterval> omittedIntervals = omittedPhaseIntervals.get(phase);
            LinkedList<SkippedInterval> runtimeIntervals = runtimeSkippedIntervalsMap.get(phase);
            LinkedList<SkippedInterval> combinedIntervals = new LinkedList<>();
            
            if (omittedIntervals != null) {
                combinedIntervals.addAll(omittedIntervals);
            }

            if (runtimeIntervals != null) {
                combinedIntervals.addAll(runtimeIntervals);
            }

            skippedIntervalsMap.put(phase, SkippedInterval.concatenateSkippedIntervals(combinedIntervals));
        }

        return skippedIntervalsMap;
    }
  
}
