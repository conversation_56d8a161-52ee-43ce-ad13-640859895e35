package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.domain.PhaseColor;
import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.analysis.util.IntersectionConfigUtils;
import com.siemens.spm.analysis.vo.LightBlockVO;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.analysis.vo.splitmonitor.ActualTimeSpaceData;
import com.siemens.spm.analysis.vo.splitmonitor.ActualTimeSpaceVO;
import com.siemens.spm.analysis.vo.splitmonitor.Phase;
import com.siemens.spm.analysis.vo.splitmonitor.ProgramedTimeSpaceVO;
import com.siemens.spm.analysis.vo.splitmonitor.Ring;
import com.siemens.spm.analysis.vo.splitmonitor.RingInformationVO;
import com.siemens.spm.analysis.vo.splitmonitor.RingStructureFactory;
import com.siemens.spm.analysis.vo.splitmonitor.SkippedInterval;
import com.siemens.spm.analysis.vo.splitmonitor.SkippedPhaseVO;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorChartVO;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorPattern;
import com.siemens.spm.analysis.vo.splitmonitor.TimeLineEventVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PatternInfoVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Slf4j
public class SplitMonitorChartBuilder extends ChartBuilder {

    private final SplitMonitorChartVO chartVO;

    private IntersectionConfigVO intConfigVO;

    private final Map<Integer, ProgramedTimeSpaceVO> programedTimeSpaceMap = new LinkedHashMap<>();

    private final Map<Integer, int[][]> sequenceDataMap = new LinkedHashMap<>();

    /**
     * Store the lights of each ring during the process of building the chart.
     */
    private final List<LightBlockVO> ring1Lights = new ArrayList<>();
    private final List<LightBlockVO> ring2Lights = new ArrayList<>();

    private final Map<Integer, LightBlockVO> lastLightPerPhase = new HashMap<>();

    private final List<ActualTimeSpaceData> cycles = new ArrayList<>();

    /**
     * The current pattern is running.
     */
    private SplitMonitorPattern currentPattern;

    /**
     * The last pattern before the current pattern.
     * Initially, there is no last pattern, so we set it to UNKNOWN_PLAN and completed.
     */
    private SplitMonitorPattern lastPattern;  // TODO might be redundant

    /**
     * The events during the process of building the chart.
     */
    private final List<TimeLineEventVO> timeLineEvents = new ArrayList<>();

    /**
     * The phases assigned to each ring during the process of building the chart.
     * The phases are ordered by the sequence.
     * The phases should be updated every time the pattern changes.
     * If there is no pattern or the pattern is FREE_PLAN, the phases should be the default phases in the config.
     */
    private List<Integer> ring1Phases;
    private List<Integer> ring2Phases;

    private Ring ring1;
    private Ring ring2;

    private List<Ring> reorderedRings;

    private static final int RING_1 = 1;
    private static final int RING_2 = 2;

    private static final int RING_COORD_PHASE_OFFSET = 30;

    private static final int THRESHOLD_SKIPPED_PHASE_DURATION_IN_SECONDS = 900;

    // phase number -> last green event time for the phase
    private final Map<Integer, LocalDateTime> lastPhaseGreenEventTimeDuringFreePlan = new HashMap<>();

    // phase number -> list of skipped phases during FREE_PLAN
    private final Map<Integer, List<SkippedPhaseVO>> skippedPhasesFreePlanOnly = new HashMap<>();

    private LocalDateTime firstFreePlanEventTime = null;

    private boolean isBuilt = false;

    private List<Pair<LocalDateTime, LocalDateTime>> gapsRanges;

    public SplitMonitorChartBuilder(SplitMonitorChartVO chartVO,
                                    Long initialPattern,
                                    List<Pair<LocalDateTime, LocalDateTime>> gapsRanges) {
        this.chartVO = chartVO;

        if (initialPattern != null) {
            this.currentPattern = SplitMonitorPattern.of(Math.toIntExact(initialPattern), false);
        } else {
            this.currentPattern = SplitMonitorPattern.of(Plan.UNKNOWN_PLAN, false);
        }
        this.lastPattern = SplitMonitorPattern.of(Plan.UNKNOWN_PLAN, true);
        this.gapsRanges = gapsRanges;
    }

    @Override
    protected void setIntersectionConfig(IntersectionConfigVO configVO) {
        this.intConfigVO = configVO;
        updatePhasesInRing();
        updateCoordPhase();
    }

    @Override
    protected void putEvent(PerfLogEventVO eventVO) {
        if (!passEventGuard(eventVO)) {
            return;
        }

        LocalDateTime eventTime = eventVO.getDateTime();
        int param = (int) eventVO.getParameter();

        switch (eventVO.getEvent()) {
            case COORD_PATTERN_CHANGE:
                if (firstFreePlanEventTime == null && param == Plan.FREE_PLAN) {
                    firstFreePlanEventTime = eventTime;
                }

                if (currentPattern.getPattern() == Plan.FREE_PLAN && param != Plan.FREE_PLAN) {
                    // close free plan when pattern changed from FREE_PLAN to NON-FREE_PLAN
                    closeFreePlan(eventTime);
                }

                // In some cases event 131 present with same pattern as previous event,
                if (currentPattern.getPattern() != param) {
                    updatePattern(param);
                }
                updateProgrammedTimeSpace(currentPattern.getPattern());
                updateSequenceData(currentPattern.getPattern());
                updatePhasesInRing();

                break;

            case PHASE_GAP_OUT, PHASE_MAX_OUT, PHASE_FORCE_OFF:
                TimeLineEventVO timeLineEvent = TimeLineEventVO.builder()
                        .event(TimeLineEventVO.Event.from(eventVO.getEvent()))
                        .time(eventTime)
                        .param((long) param)
                        .build();
                timeLineEvents.add(timeLineEvent);
                break;

            case PHASE_BEGIN_GREEN: {
                log.info("Current pattern: {} with green event {}", currentPattern.getPattern(), eventTime);
                aggregateLight(param, eventTime, PhaseColor.GREEN);

                if (currentPattern.getPattern() == Plan.FREE_PLAN) {
                    handleBeginGreenForFreePlan(eventTime, param);
                }

                break;
            }

            case PHASE_BEGIN_YELLOW_CLEARANCE: {
                aggregateLight(param, eventTime, PhaseColor.YELLOW);
                break;
            }

            case PHASE_BEGIN_RED_CLEARANCE: {
                aggregateLight(param, eventTime, PhaseColor.RED);
                break;
            }

            default:
                // Do Nothing
        }
    }

    private void handleBeginGreenForFreePlan(LocalDateTime eventTime, int param) {
        // Firstly, check the following case:
        // 254--------FROM---------------green(phase_X)------green(phase_X)

        // [1] firstFreePlanEventTime is null
        //     => current plan is FREE_PLAN but no COORD_PATTERN_CHANGE(254) so far
        //     => meaning: FREE_PLAN started before the chart's from time
        // [2] lastPhaseGreenEventTimeDuringFreePlan does not contains key "param"
        //     => meaning: 1st green for the phase after chart's from time
        // if [1] and [2] are true
        //     => check if the duration between the 1st green and the chart's from time is significant
        if (firstFreePlanEventTime == null && !lastPhaseGreenEventTimeDuringFreePlan.containsKey(param)) {
            // [1] and [2] are true
            // skipped phase if duration between the 1st green and the chart's from time is significant
            Duration timeSinceFromTime = Duration.between(chartVO.getFromTime(), eventTime);
            if (timeSinceFromTime.toSeconds() >= THRESHOLD_SKIPPED_PHASE_DURATION_IN_SECONDS) {
                SkippedPhaseVO skippedPhase = new SkippedPhaseVO(param, (int) timeSinceFromTime.toSeconds());
                skippedPhase.setStartTime(chartVO.getFromTime());
                skippedPhase.setEndTime(eventTime);
                skippedPhasesFreePlanOnly
                        .computeIfAbsent(param, k -> new ArrayList<>())
                        .add(skippedPhase);
            }
        }

        detectSkippedPhase(param, eventTime);
        lastPhaseGreenEventTimeDuringFreePlan.put(param, eventTime);
    }

    private void updatePattern(int newPattern) {
        aggregateCycles(currentPattern, true);
        lastPattern = currentPattern;

        if (newPattern == Plan.UNKNOWN_PLAN || newPattern == Plan.FREE_PLAN) {
            lastPattern.complete();
            currentPattern = SplitMonitorPattern.of(newPattern, false);
            return;
        }

        Integer ring1CoordPhase = IntersectionConfigUtils.getCoordinatedPhaseInRing(intConfigVO, newPattern, RING_1);
        Integer ring2CoordPhase = IntersectionConfigUtils.getCoordinatedPhaseInRing(intConfigVO, newPattern, RING_2);
        currentPattern = SplitMonitorPattern.of(newPattern, ring1CoordPhase, ring2CoordPhase);
    }

    private void updatePhasesInRing() {
        ring1Phases = IntersectionConfigUtils.phaseSequenceInRing(intConfigVO, currentPattern.getPattern(), RING_1);
        ring2Phases = IntersectionConfigUtils.phaseSequenceInRing(intConfigVO, currentPattern.getPattern(), RING_2);

        List<Phase> ring1PhasesObj = new ArrayList<>();
        List<Phase> ring2PhasesObj = new ArrayList<>();

        for (Integer phase : ring1Phases) {
            ring1PhasesObj.add(new Phase(phase, IntersectionConfigUtils.isCoordinatedPhase(phase, intConfigVO)));
        }

        for (Integer phase : ring2Phases) {
            ring2PhasesObj.add(new Phase(phase, IntersectionConfigUtils.isCoordinatedPhase(phase, intConfigVO)));
        }

        reorderedRings = RingStructureFactory.determineRingStructure(
                List.of(new Ring(ring1PhasesObj), new Ring(ring2PhasesObj)));
    }

    /**
     * In case of unknown plan or free plan, we don't have the ring information.
     * So we will ignore the coordinated phase
     */
    private void updateCoordPhase() {
        if (currentPattern.getPattern() == Plan.UNKNOWN_PLAN
                || currentPattern.getPattern() == Plan.FREE_PLAN) {
            return;
        }

        int ring1CoordPhase = IntersectionConfigUtils.getCoordinatedPhaseInRing(intConfigVO,
                currentPattern.getPattern(), RING_1);
        int ring2CoordPhase = IntersectionConfigUtils.getCoordinatedPhaseInRing(intConfigVO,
                currentPattern.getPattern(), RING_2);
        currentPattern.setRing1CoordPhase(ring1CoordPhase);
        currentPattern.setRing2CoordPhase(ring2CoordPhase);
    }

    private boolean passEventGuard(PerfLogEventVO eventVO) {
        LocalDateTime eventTime = eventVO.getDateTime();
        return eventTime != null
                && eventTime.isAfter(chartVO.getFromTime())
                && eventTime.isBefore(chartVO.getToTime())
                && eventVO.getEvent() != null;
    }

    private void updateProgrammedTimeSpace(int pattern) {
        if (pattern == Plan.UNKNOWN_PLAN || pattern == Plan.FREE_PLAN) {
            return;
        }

        PatternInfoVO patternInfoVO = intConfigVO.getPatternInfo();

        int[][] rings = getRingStructure(pattern, patternInfoVO);
        int[] splitTimesOfPattern = patternInfoVO.getSplitTimeOfPattern(pattern - 1);
        Integer ring1CoordPhase = IntersectionConfigUtils.getCoordinatedPhaseInRing(intConfigVO, pattern, RING_1);
        Integer ring2CoordPhase = IntersectionConfigUtils.getCoordinatedPhaseInRing(intConfigVO, pattern, RING_2);

        ProgramedTimeSpaceVO programedTimeSpaceVO = ProgramedTimeSpaceVO.builder()
                .ring1(simulateLights(ring1CoordPhase, rings[RING_1 - 1], splitTimesOfPattern))
                .ring2(simulateLights(ring2CoordPhase, rings[RING_2 - 1], splitTimesOfPattern))
                .offsetTime(patternInfoVO.getOffsetTime()[pattern - 1])
                .offsetTimeUnit(ChronoUnit.SECONDS)
                .noPlan(pattern)
                .coordPhase(patternInfoVO.getSplitCoordPhasesOfPattern(pattern - 1))
                .build();

        programedTimeSpaceMap.put(pattern, programedTimeSpaceVO);
    }

    private void updateSequenceData(int pattern) {
        if (pattern == Plan.UNKNOWN_PLAN || pattern == Plan.FREE_PLAN) {
            return;
        }
        PatternInfoVO patternInfoVO = intConfigVO.getPatternInfo();
        int[][] rings = getRingStructure(pattern, patternInfoVO);
        sequenceDataMap.put(pattern, rings);
    }

    private int[][] getRingStructure(int pattern, PatternInfoVO patternInfoVO) {
        // there are 16 sequences => sequence number from 1 to 16
        // 0 is special value => denotes default sequence
        int sequenceNumber = patternInfoVO.getSequenceNumber()[pattern - 1];

        if (sequenceNumber <= 0 || sequenceNumber > 16) {
            // default sequence must be between 1 and 16
            sequenceNumber = intConfigVO.getSequenceInfo().getDefaultSequence();
            if (sequenceNumber == 0) {
                log.warn("Default sequence in config is 0 which is NOT valid!!! Take sequence 1");
                sequenceNumber = 1;
            }
        }

        return intConfigVO.getRingStructureInfo().getSequenceData()[sequenceNumber - 1];
    }

    private List<LightBlockVO> simulateLights(Integer coordPhase, int[] sequencePhases, int[] phaseSplitTime) {
        List<Integer> phasesSequenceFromCoord = phasesSequenceFromCoordPhase(coordPhase, sequencePhases);
        List<PhaseColor> lightColors = List.of(PhaseColor.GREEN, PhaseColor.YELLOW, PhaseColor.RED);
        LocalDateTime startTime = LocalDateTime.of(2000, 1, 1, 0, 0, 0);

        List<LightBlockVO> lights = new ArrayList<>();
        for (Integer phase : phasesSequenceFromCoord) {
            for (PhaseColor color : lightColors) {
                int splitTime = phaseSplitTime[phase - 1];
                LocalDateTime endTime = startTime.plusSeconds(color.getOffset(splitTime));
                LightBlockVO lightBlockVO = LightBlockVO.builder()
                        .phase(phase)
                        .color(color.name())
                        .isCoordPhase(Objects.equals(phase, coordPhase))
                        .startTime(startTime)
                        .endTime(endTime)
                        .build();
                startTime = endTime;
                lights.add(lightBlockVO);
            }
        }
        return lights;
    }

    /**
     * Returns a reordered list of phases starting from the coordinated phase.
     * If the coordinated phase is null or not found, returns the original order.
     *
     * @param coordPhase     The coordinated phase (may be null)
     * @param sequencePhases The sequence of phases in the ring
     * @return The reordered sequence of phases starting from the coordinated phase
     */
    private List<Integer> phasesSequenceFromCoordPhase(Integer coordPhase, int[] sequencePhases) {
        if (coordPhase == null) {
            return Arrays.stream(sequencePhases)
                    .boxed()
                    .toList();
        }

        // Find the index of the coordinated phase
        int coordIdx = -1;
        for (int i = 0; i < sequencePhases.length; i++) {
            if (sequencePhases[i] == coordPhase) {
                coordIdx = i;
                break;
            }
        }

        // If not found, return the original order
        if (coordIdx == -1) {
            return Arrays.stream(sequencePhases)
                    .boxed()
                    .toList();
        }

        // Reorder: from coordIdx to end, then from start to coordIdx-1
        List<Integer> reordered = new ArrayList<>(sequencePhases.length);
        for (int i = coordIdx; i < sequencePhases.length; i++) {
            reordered.add(sequencePhases[i]);
        }
        for (int i = 0; i < coordIdx; i++) {
            reordered.add(sequencePhases[i]);
        }
        return reordered;
    }

    private void aggregateLight(int phase,
                                LocalDateTime eventTime,
                                PhaseColor newLightColor) {
        LightBlockVO lastBlock = lastLightPerPhase.remove(phase);
        if (lastBlock != null) {
            lastBlock.setEndTime(eventTime);

            if (ring1Phases.contains(phase)) {
                currentPattern.getRing1Lights().add(lastBlock);
            } else if (ring2Phases.contains(phase)) {
                currentPattern.getRing2Lights().add(lastBlock);
            }
        }

        LightBlockVO lightDataBlock = LightBlockVO.builder()
                .phase(phase)
                .color(newLightColor.name())
                .startTime(eventTime)
                .isCoordPhase(
                        phase == currentPattern.getRing1CoordPhase() || phase == currentPattern.getRing2CoordPhase())
                .build();

        // Add the light for the whole timelines
        if (ring1Phases.contains(phase)) {
            ring1Lights.add(lightDataBlock);
        } else {
            ring2Lights.add(lightDataBlock);
        }
        lastLightPerPhase.put(phase, lightDataBlock);
    }

    private void aggregateCycles(SplitMonitorPattern splitMonitorPattern, boolean keepLastCycle) {
        int pattern = splitMonitorPattern.getPattern();

        List<LightBlockVO> ring1Lights = getFineTuneCycles(splitMonitorPattern.getRing1Lights(), gapsRanges);
        List<LightBlockVO> ring2Lights = getFineTuneCycles(splitMonitorPattern.getRing2Lights(), gapsRanges);

        int index1 = 0;
        int index2 = 0;
        for (Pair<LocalDateTime, LocalDateTime> gapsRange : gapsRanges) {
            List<LightBlockVO> ring1SplitLights = new ArrayList<>();
            List<LightBlockVO> ring2SplitLights = new ArrayList<>();

            while (index1 < ring1Lights.size() &&
                    !ring1Lights.get(index1).getEndTime().isAfter(gapsRange.getLeft())) {
                ring1SplitLights.add(ring1Lights.get(index1));
                index1++;
            }

            while (index2 < ring2Lights.size() &&
                    !ring2Lights.get(index2).getEndTime().isAfter(gapsRange.getLeft())) {
                ring2SplitLights.add(ring2Lights.get(index2));
                index2++;
            }

            processCycle(keepLastCycle, pattern, ring1SplitLights, ring2SplitLights);
        }

        List<LightBlockVO> remainingRing1SplitLights = new ArrayList<>();
        List<LightBlockVO> remainingRing2SplitLights = new ArrayList<>();

        while (index1 < ring1Lights.size()) {
            remainingRing1SplitLights.add(ring1Lights.get(index1));
            index1++;
        }

        while (index2 < ring2Lights.size()) {
            remainingRing2SplitLights.add(ring2Lights.get(index2));
            index2++;
        }
        processCycle(keepLastCycle, pattern, remainingRing1SplitLights, remainingRing2SplitLights);

    }

    private boolean processCycle(boolean keepLastCycle,
                                 int pattern,
                                 List<LightBlockVO> ring1SplitLights,
                                 List<LightBlockVO> ring2SplitLights) {
        if (pattern == Plan.UNKNOWN_PLAN || pattern == Plan.FREE_PLAN) {
            createCycle(ring1SplitLights, ring2SplitLights, pattern).ifPresent(cycles::add);
            return true;
        }

        if (ring1SplitLights.isEmpty() && ring2SplitLights.isEmpty()) {
            return true;
        }

        Queue<LightBlockVO> ring1LightQueue = new ArrayDeque<>(ring1SplitLights);
        Queue<LightBlockVO> ring2LightQueue = new ArrayDeque<>(ring2SplitLights);

        while (!ring1LightQueue.isEmpty() && !ring2LightQueue.isEmpty()) {
            LightBlockVO ring1CoordGreenLight = ring1LightQueue.peek();
            LightBlockVO ring2CoordGreenLight = ring2LightQueue.peek();

            List<LightBlockVO> ring1CycleLights = new ArrayList<>();
            List<LightBlockVO> ring2CycleLights = new ArrayList<>();

            if (isSameCycle(ring1CoordGreenLight, ring2CoordGreenLight)) {
                ring1CycleLights = pollUntilCoordPhase(ring1LightQueue);
                ring2CycleLights = pollUntilCoordPhase(ring2LightQueue);
            } else {
                if (ring1CoordGreenLight.getStartTime().isBefore(ring2CoordGreenLight.getStartTime())) {
                    ring1CycleLights = pollUntilCoordPhase(ring1LightQueue);
                } else {
                    ring2CycleLights = pollUntilCoordPhase(ring2LightQueue);
                }
            }

            createCycle(ring1CycleLights, ring2CycleLights, pattern).ifPresent(cycles::add);
        }

        if (ring1LightQueue.isEmpty() && ring2LightQueue.isEmpty()) {
            if (!keepLastCycle) {
                // Remove the last cycle from the list
                cycles.remove(cycles.size() - 1);
            }
            return true;
        } else {
            if (keepLastCycle) {
                // Add all remaining lights to the last cycle
                if (!ring1LightQueue.isEmpty()) {
                    createCycle(new ArrayList<>(ring1LightQueue), new ArrayList<>(), pattern).ifPresent(
                            cycles::add);
                }
                if (!ring2LightQueue.isEmpty()) {
                    createCycle(new ArrayList<>(), new ArrayList<>(ring2LightQueue), pattern).ifPresent(
                            cycles::add);
                }
            }
        }
        return false;
    }

    // Will add the end of all light block missing endTime
    private void closeLightsBlocks(LocalDateTime toTime) {
        lastLightPerPhase.values().forEach(light -> {
            if (light.getEndTime() == null) {
                light.setEndTime(toTime);
            }
            if (ring1Phases.contains(light.getPhase())) {
                ring1Lights.add(light);
                currentPattern.addRing1Light(light);
            } else if (ring2Phases.contains(light.getPhase())) {
                ring2Lights.add(light);
                currentPattern.addRing2Light(light);
            }
        });
    }

    @Override
    protected void build() {
        closeLightsBlocks(chartVO.getToTime());

        closeFreePlan(chartVO.getToTime());

        aggregateCycles(currentPattern, true);

        reformatRingTimeline(ring1Lights);
        reformatRingTimeline(ring2Lights);

        Optional<ActualTimeSpaceData> wholeCycleOptional = createCycle(ring1Lights, ring2Lights, Plan.UNKNOWN_PLAN);
        if (wholeCycleOptional.isEmpty()) {
            isBuilt = true;
            return;
        }

        ActualTimeSpaceData wholeCycle = wholeCycleOptional.get();
        wholeCycle.setTimeLineEvents(new TreeSet<>(timeLineEvents));

        chartVO.setActualTimeSpace(calculateCyclesStatistics(cycles, chartVO, wholeCycle));
        chartVO.setProgramedTimeSpace(programedTimeSpaceMap);
        chartVO.setSequenceData(sequenceDataMap);

        isBuilt = true;
    }

    private void closeFreePlan(LocalDateTime toTime) {
        lastPhaseGreenEventTimeDuringFreePlan.keySet().forEach(phase ->
                // check if duration between last green and the end time of the chart is significant
                detectSkippedPhase(phase, toTime));

        lastPhaseGreenEventTimeDuringFreePlan.clear();
    }

    public Map<Integer, LinkedList<SkippedInterval>> skippedPhaseIntervals() {
        if (!isBuilt) {
            build();
        }

        ActualTimeSpaceVO actualTimeSpace = chartVO.getActualTimeSpace();
        if (actualTimeSpace == null || actualTimeSpace.getDataCycles() == null) {
            return Map.of();
        }

        actualTimeSpace.setSkippedPhasesDuringFreePlan(skippedPhasesFreePlanOnly);
        return actualTimeSpace.skippedPhaseIntervals();
    }

    private ActualTimeSpaceVO calculateCyclesStatistics(List<ActualTimeSpaceData> cycles,
                                                        SplitMonitorChartVO chartVO,
                                                        ActualTimeSpaceData wholeCycle) {
        CycleStatistics stats = cycles.stream()
                .map(cycle -> new CycleStatistics(
                        cycle.getRing1().getSkippedPhases().size() + cycle.getRing2().getSkippedPhases().size(),
                        cycle.getRing1().getSkippedDuration() + cycle.getRing2().getSkippedDuration(),
                        Math.min(cycle.getRing1().getDuration(), cycle.getRing2().getDuration()),
                        Math.max(cycle.getRing1().getDuration(), cycle.getRing2().getDuration())
                ))
                .reduce(new CycleStatistics(), CycleStatistics::combine);

        return ActualTimeSpaceVO.builder()
                .fromTime(chartVO.getFromTime())
                .toTime(chartVO.getToTime())
                .noCycle(cycles.size())
                .noSkippedPhase(stats.totalSkippedPhases)
                .skippedPhaseDuration(stats.totalSkippedDuration)
                .minCycleLength(stats.minCycleLength)
                .maxCycleLength(stats.maxCycleLength)
                .timeline(wholeCycle)
                .dataCycles(cycles)
                .build();
    }

    private Optional<ActualTimeSpaceData> createCycle(List<LightBlockVO> ring1Lights,
                                                      List<LightBlockVO> ring2Lights,
                                                      int pattern) {
        if (ring1Lights.isEmpty() || ring2Lights.isEmpty()) {
            return Optional.empty();
        }

        List<LightBlockVO> programedRing1Lights;
        List<LightBlockVO> programedRing2Lights;

        ProgramedTimeSpaceVO programedTimeSpace = programedTimeSpaceMap.get(pattern);
        if (programedTimeSpace != null) {
            programedRing1Lights = programedTimeSpace.getRing1();
            programedRing2Lights = programedTimeSpace.getRing2();
        } else {
            programedRing1Lights = new ArrayList<>();
            programedRing2Lights = new ArrayList<>();
        }

        RingInformationVO ring1Cycle = RingInformationVO.of(RING_1, ring1Lights, programedRing1Lights);
        RingInformationVO ring2Cycle = RingInformationVO.of(RING_2, ring2Lights, programedRing2Lights);
        ActualTimeSpaceData cycle = ActualTimeSpaceData.builder()
                .ring1(ring1Cycle)
                .ring2(ring2Cycle)
                .plan(pattern)
                .build();

        LocalDateTime startTime = DateTimeUtil.min(cycle.getRing1().getStartTime(), cycle.getRing2().getStartTime());
        LocalDateTime endTime = DateTimeUtil.max(cycle.getRing1().getEndTime(), cycle.getRing2().getEndTime());

        cycle.setStartTime(startTime);
        cycle.setEndTime(endTime);

        Set<TimeLineEventVO> cycleTimelineEvents = timeLineEvents.stream()
                .filter(event -> event.getTime().isAfter(startTime) && event.getTime().isBefore(endTime))
                .sorted()
                .collect(Collectors.toCollection(TreeSet::new));
        cycle.setTimeLineEvents(cycleTimelineEvents);

        return Optional.of(cycle);
    }

    /**
     * Fine-tune the cycles by updating the end time of the previous light to the start time of the next light
     *
     * @param lights The lights need to be fine-tuned
     * @return The fine-tuned lights
     */
    private List<LightBlockVO> getFineTuneCycles(List<LightBlockVO> lights,
                                                 List<Pair<LocalDateTime, LocalDateTime>> gapsRanges) {

        // TODO: Need to consider why we need to clone the lights list
        List<LightBlockVO> cloneLights = new ArrayList<>();
        for (LightBlockVO light : lights) {
            cloneLights.add(light.cloneObject());
        }

        cleanupInvalidLightBlock(cloneLights);
        return updateSplitLights(lights, gapsRanges, cloneLights);

    }

    private void cleanupInvalidLightBlock(List<LightBlockVO> cloneLights) {
        // Update the end time of previous light to the start time of the next light
        Iterator<LightBlockVO> iterator = cloneLights.iterator();
        LightBlockVO currentLight = null;
        if (iterator.hasNext()) {
            currentLight = iterator.next();
        }

        List<LightBlockVO> toRemove = new ArrayList<>();

        while (iterator.hasNext()) {
            LightBlockVO nextLight = iterator.next();
            if (currentLight != null && currentLight.getColor()
                    .equals(nextLight.getColor()) && currentLight.getEndTime().isAfter(nextLight.getStartTime())) {
                toRemove.add(currentLight);
            } else {
                if (currentLight != null) {
                    currentLight.setEndTime(nextLight.getStartTime());
                }
            }
            currentLight = nextLight;
        }

        cloneLights.removeAll(toRemove);
    }

    private List<LightBlockVO> updateSplitLights(List<LightBlockVO> lights,
                                                 List<Pair<LocalDateTime, LocalDateTime>> gapsRanges,
                                                 List<LightBlockVO> cloneLights) {
        for (int i = 0; i < cloneLights.size(); i++) {
            LightBlockVO light = cloneLights.get(i);
            for (Pair<LocalDateTime, LocalDateTime> gapRange : gapsRanges) {
                LocalDateTime startGap = gapRange.getLeft();
                LocalDateTime endGap = gapRange.getRight();
                LocalDateTime startLight = light.getStartTime();
                LocalDateTime endLight = light.getEndTime();
                if (startLight.isBefore(startGap) && endLight.isAfter(endGap)) {
                    splitLights(lights, i, startGap, endGap);
                    splitLights(cloneLights, i, startGap, endGap);
                }
            }
        }
        return cloneLights;
    }

    private void splitLights(List<LightBlockVO> lights,
                             int index,
                             LocalDateTime newEndRange,
                             LocalDateTime newStartRange) {
        LightBlockVO originalLight = lights.get(index);

        LightBlockVO leftLight = createSplitLight(originalLight, originalLight.getStartTime(), newEndRange);
        LightBlockVO rightLight = createSplitLight(originalLight, newStartRange, originalLight.getEndTime());

        lights.set(index, leftLight);
        lights.add(rightLight);
    }

    private LightBlockVO createSplitLight(LightBlockVO source, LocalDateTime startTime, LocalDateTime endTime) {
        LightBlockVO splitLight = new LightBlockVO();
        splitLight.setStartTime(startTime);
        splitLight.setEndTime(endTime);
        splitLight.setColor(source.getColor());
        splitLight.setPlan(source.getPlan());
        splitLight.setPhase(source.getPhase());
        splitLight.setCoordPhase(source.isCoordPhase());
        splitLight.setConfigId(source.getConfigId());
        splitLight.setRing(source.getRing());
        return splitLight;
    }

    private List<LightBlockVO> pollUntilCoordPhase(Queue<LightBlockVO> lightQueue) {
        List<LightBlockVO> lights = new ArrayList<>();

        while (!lightQueue.isEmpty()) {
            LightBlockVO light = lightQueue.peek();
            if (light.isGreenCoordPhase() && !lights.isEmpty()) {
                break;
            }
            lights.add(lightQueue.poll());
        }
        return lights;
    }

    private boolean isSameCycle(LightBlockVO ring1Light, LightBlockVO ring2Light) {
        Duration duration = DateTimeUtil.duration(ring1Light.getStartTime(), ring2Light.getStartTime())
                .orElse(Duration.ZERO);
        return Math.abs(duration.toSeconds()) < RING_COORD_PHASE_OFFSET;
    }

    private void reformatRingTimeline(List<LightBlockVO> ringLights) {
        ringLights.sort(LightBlockVO::compareTo);
        for (int i = 0; i < ringLights.size(); i++) {
            LightBlockVO light = ringLights.get(i);
            for (Pair<LocalDateTime, LocalDateTime> gapRange : gapsRanges) {
                LocalDateTime startGap = gapRange.getLeft();
                LocalDateTime endGap = gapRange.getRight();
                LocalDateTime startLight = light.getStartTime();
                LocalDateTime endLight = light.getEndTime();
                if (startLight.isBefore(startGap) && endLight.isAfter(endGap)) {
                    splitLights(ringLights, i, startGap, endGap);
                }
            }
        }
    }

    /**
     * Checks if there was a significant gap between consecutive green phases that indicates a skipped phase.
     * Only applies to FREE_PLAN operation mode.
     *
     * @param phase     The traffic signal phase number
     * @param eventTime The timestamp of the current green event
     */
    private void detectSkippedPhase(int phase, LocalDateTime eventTime) {
        if (currentPattern.getPattern() != Plan.FREE_PLAN) {
            return;
        }

        LocalDateTime lastGreenEventTime = lastPhaseGreenEventTimeDuringFreePlan.get(phase);
        if (lastGreenEventTime == null) {
            return; // No previous green event for this phase
        }

        long secondsSinceLastGreen = Duration.between(lastGreenEventTime, eventTime).toSeconds();
        if (secondsSinceLastGreen >= THRESHOLD_SKIPPED_PHASE_DURATION_IN_SECONDS) {
            SkippedPhaseVO skippedPhase = new SkippedPhaseVO(phase, (int) secondsSinceLastGreen);
            skippedPhase.setStartTime(lastGreenEventTime);
            skippedPhase.setEndTime(eventTime);

            log.warn("Detected skipped phase: {} with duration: {} seconds",
                    phase, secondsSinceLastGreen);

            skippedPhasesFreePlanOnly
                    .computeIfAbsent(phase, k -> new ArrayList<>())
                    .add(skippedPhase);
        }
    }

    private static class CycleStatistics {

        int totalSkippedPhases;
        int totalSkippedDuration;
        int minCycleLength;
        int maxCycleLength;

        CycleStatistics() {
            this.totalSkippedPhases = 0;
            this.totalSkippedDuration = 0;
            this.minCycleLength = Integer.MAX_VALUE;
            this.maxCycleLength = Integer.MIN_VALUE;
        }

        CycleStatistics(int skippedPhases, int skippedDuration, int minLength, int maxLength) {
            this.totalSkippedPhases = skippedPhases;
            this.totalSkippedDuration = skippedDuration;
            this.minCycleLength = minLength;
            this.maxCycleLength = maxLength;
        }

        CycleStatistics combine(CycleStatistics other) {
            CycleStatistics result = new CycleStatistics();
            result.totalSkippedPhases = this.totalSkippedPhases + other.totalSkippedPhases;
            result.totalSkippedDuration = this.totalSkippedDuration + other.totalSkippedDuration;
            result.minCycleLength = Math.min(this.minCycleLength, other.minCycleLength);
            result.maxCycleLength = Math.max(this.maxCycleLength, other.maxCycleLength);
            return result;
        }

    }

}
