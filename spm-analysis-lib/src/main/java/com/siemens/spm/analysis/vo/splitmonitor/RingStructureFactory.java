package com.siemens.spm.analysis.vo.splitmonitor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 30/6/2025
 **/
public final class RingStructureFactory {

    private RingStructureFactory() {
    }

    /**
     * Determines the ring structure based on coordinated phases.
     *
     * @param rings List of rings containing phases
     * @return reordered rings with sequence starting from the coordinated phase
     */
    public static List<Ring> determineRingStructure(List<Ring> rings) {
        List<Phase> coordinatedPhases = new ArrayList<>();
        for (Ring ring : rings) {
            for (Phase phase : ring.getPhases()) {
                if (phase.isCoordinated()) {
                    coordinatedPhases.add(phase);
                }
            }
        }

        if (coordinatedPhases.isEmpty()) {
            throw new IllegalArgumentException("No coordinated phases found");
        }

        int minIndex = Integer.MAX_VALUE;
        for (Phase phase : coordinatedPhases) {
            for (Ring ring : rings) {
                int index = ring.getPhases().indexOf(phase);
                if (index != -1) {
                    if (index < minIndex) {
                        minIndex = index;
                    }
                    break;
                }
            }
        }

        List<Ring> reorderedRings = new ArrayList<>();
        for (Ring ring : rings) {
            List<Phase> original = ring.getPhases();
            List<Phase> rotated = new ArrayList<>();
            int n = original.size();
            // Rotate the ring to start from minIndex
            for (int i = 0; i < n; i++) {
                rotated.add(original.get((minIndex + i) % n));
            }
            reorderedRings.add(new Ring(rotated));
        }

        return reorderedRings;
    }

}
