package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.SplitFailureAggregator;
import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorAnalysisVO;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.COORD_PATTERN_CHANGE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_GREEN;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_FORCE_OFF;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GAP_OUT;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GREEN_TERMINATION;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_MAX_OUT;

/**
 * During FREE_PLAN, a phase is considered as skipped if the duration between 2 consecutive greens > 900 seconds.
 *
 * @see SplitFailureAggregator#putEvent List of events processed
 */
public class SplitMonitorAnalysisFactory extends AbstractAnalysisFactory<SplitMonitorAnalysisVO, SplitMonitorChartVO> {

    // Events used for split monitor analysis
    // TODO: It will be supported in the future when request to datasource
    private final List<PerfLogEventVO.Event> SPLIT_MONITOR_EVENT_FILTERS = List.of(
            COORD_PATTERN_CHANGE, // 131
            PHASE_GAP_OUT, // 4
            PHASE_MAX_OUT, // 5
            PHASE_FORCE_OFF, // 6
            PHASE_BEGIN_GREEN,  // 1
            PHASE_GREEN_TERMINATION, // 7
            PHASE_BEGIN_YELLOW_CLEARANCE, // 8
            PHASE_END_YELLOW_CLEARANCE, // 9
            PHASE_BEGIN_RED_CLEARANCE // 10
    );

    @Getter
    @Setter
    protected Long initialPattern;

    @Override
    public SplitMonitorAnalysisVO createAnalysis(LocalDateTime fromTime,
                                                 LocalDateTime toTime,
                                                 PerfLogBundleVO perfLogBundleVO,
                                                 List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException {
        return createAnalysis(fromTime, toTime,
                perfLogBundleVO, perfLogGapVOList,
                SplitMonitorAnalysisVO.class, SplitMonitorChartVO.class);
    }

    @Override
    protected void buildCharts(LocalDateTime fromTime,
                               LocalDateTime toTime,
                               PerfLogBundleVO perfLogBundleVO,
                               Class<SplitMonitorChartVO> splitMonitorChartVOClass)
            throws InvalidPerfLogException {
        List<PerfLogChunkVO> perfLogChunkVOList = perfLogBundleVO.getPerfLogChunks();
        Map<String, IntersectionConfigVO> intConfigMap = perfLogBundleVO.getIntConfigs();
        if (perfLogChunkVOList == null || intConfigMap == null) {
            throw new InvalidPerfLogException("Missing PerfLog chunk or intersection config");
        }

        SplitMonitorChartVO chartVO = new SplitMonitorChartVO();
        chartVO.setFromTime(fromTime);
        chartVO.setToTime(toTime);

        List<PerfLogGapVO> perfLogGaps = analysisVO.getPerfLogGapList();
        List<Pair<LocalDateTime, LocalDateTime>> gapsRanges = perfLogGaps.stream()
                .map(g -> Pair.of(g.getFromTime(), g.getToTime())).toList();

        SplitMonitorChartBuilder builder = new SplitMonitorChartBuilder(chartVO, initialPattern, gapsRanges);
        for (PerfLogChunkVO chunkVO : perfLogChunkVOList) {
            IntersectionConfigVO intConfigVO = intConfigMap.get(chunkVO.getConfigID());
            if (intConfigVO == null) {
                throw new InvalidPerfLogException("Missing intersection config for chunk " + chunkVO.getConfigID());
            }
            builder.setIntersectionConfig(intConfigVO);
            List<PerfLogEventVO> filterEvents = chunkVO.getPerfLogEvents().stream()
                    .filter(p -> SPLIT_MONITOR_EVENT_FILTERS.contains(p.getEvent())).toList();
            for (PerfLogEventVO perfLogEvent : filterEvents) {
                if (!isEventInGap(perfLogEvent, perfLogGaps)) {
                    builder.putEvent(perfLogEvent);
                }

            }
        }
        builder.build();

        analysisVO.addChart(chartVO);
    }

    private boolean isEventInGap(PerfLogEventVO event, List<PerfLogGapVO> gaps) {
        return gaps.stream()
                .anyMatch(gap -> event.getDateTime().isAfter(gap.getFromTime()) &&
                        event.getDateTime().isBefore(gap.getToTime()));
    }

}
