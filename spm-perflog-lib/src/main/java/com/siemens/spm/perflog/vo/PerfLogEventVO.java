/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogEventVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.siemens.spm.common.util.BeanFinder;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@ToString
@Builder
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PerfLogEventVO implements Serializable, Comparable<PerfLogEventVO> {

    // WARN: Currently, event name used as text key to translate. Be careful when change name
    // TODO: Use text key as a field instead of name directed
    public enum Event {

        /**
         * Fake this event to present no perflog data event of DataHub
         */
        NO_DATA(-1),

        /**
         * Normal events from DataHub
         */
        PHASE_ON(0),
        PHASE_BEGIN_GREEN(1),
        PHASE_CHECK(2),
        PHASE_MIN_COMPLETE(3),
        PHASE_GAP_OUT(4),
        PHASE_MAX_OUT(5),
        PHASE_FORCE_OFF(6),
        PHASE_GREEN_TERMINATION(7),
        PHASE_BEGIN_YELLOW_CLEARANCE(8),
        PHASE_END_YELLOW_CLEARANCE(9),
        PHASE_BEGIN_RED_CLEARANCE(10),
        PHASE_END_RED_CLEARANCE(11),
        PHASE_INACTIVE(12),
        /**
         * Pseudo-event, not part of IDOT enumeration
         */
        PHASE_SKIP(20),

        /**
         * Active Pedestrian Events
         */
        PEDESTRIAN_BEGIN_WALK(21),
        PEDESTRIAN_BEGIN_CLEARANCE(22),
        PEDESTRIAN_BEGIN_NOT_WALK(23),
        PEDESTRIAN_DARK(24),

        /**
         * Barrier / Ring Events
         */
        BARRIER_TERMINATION(31),
        FYA_BEGIN_PERMISSIVE(32),
        FYA_END_PERMISSIVE(33),

        /**
         * Phase Control Events
         */
        PHASE_HOLD_ACTIVE(41),
        PHASE_HOLD_RELEASED(42),
        PHASE_CALL_REGISTERED(43),
        PHASE_CALL_DROPPED(44),
        PEDESTRIAN_CALL_REGISTERED(45),
        PHASE_OMIT_ON(46),
        PHASE_OMIT_OFF(47),
        PEDESTRIAN_OMIT_ON(48),
        PEDESTRIAN_OMIT_OFF(49),

        /**
         * Overlap Events
         */
        OVERLAP_BEGIN_GREEN(61),
        OVERLAP_BEGIN_TRAILING_GREEN(62),
        OVERLAP_BEGIN_YELLOW(63),
        OVERLAP_BEGIN_RED_CLEARANCE(64),
        OVERLAP_OFF(65),
        OVERLAP_DARK(66),
        PEDESTRIAN_OVERLAP_BEGIN_WALK(67),
        PEDESTRIAN_OVERLAP_BEGIN_CLEARANCE(68),
        PEDESTRIAN_OVERLAP_BEGIN_SOLID_NOT_WALK(69),
        PEDESTRIAN_OVERLAP_DARK(70),

        /**
         * Detector Events
         */
        DETECTOR_OFF(81),
        DETECTOR_ON(82),
        DETECTOR_RESTORED(83),
        DETECTOR_FAULT_OTHER(84),
        DETECTOR_FAULT_WATCHDOG(85),
        DETECTOR_FAULT_OPEN_LOOP_FAULT(86),
        DETECTOR_FAULT_SHORTED_LOOP_FAULT(87),
        DETECTOR_FAULT_EXCESSIVE_CHANGE_FAULT(88),
        PED_DETECTOR_OFF(89),
        PED_DETECTOR_ON(90),
        PEDESTRIAN_DETECTOR_FAILED(91),
        PEDESTRIAN_DETECTOR_RESTORED(92),

        /**
         * Preemption Events
         */
        PREEMPT_ADVANCE_WARNING_INPUT(101),
        PREEMPT_INPUT_ON(102),
        PREEMPT_GATE_DOWN_INPUT_RECEIVED(103),
        PREEMPT_INPUT_OFF(104),
        PREEMPT_ENTRY_STARTED(105),
        PREEMPTION_BEGIN_TRACK_CLEARANCE(106),
        PREEMPTION_BEGIN_DWELL_SERVICE(107),
        PREEMPTION_LINK_ACTIVE_ON(108),
        PREEMPTION_LINK_ACTIVE_OFF(109),
        PREEMPTION_MAX_PRESENCE_EXCEEDED(110),
        PREEMPTION_BEGIN_EXIT_INTERVAL(111),
        TSP_CHECK_IN(112),
        TSP_ADJUSTMENT_TO_EARLY_GREEN(113),
        TSP_ADJUSTMENT_TO_EXTEND_GREEN(114),
        TSP_CHECK_OUT(115),

        /**
         * Coordination Events
         */
        COORD_PATTERN_CHANGE(131),
        CYCLE_LENGTH_CHANGE(132),
        OFFSET_LENGTH_CHANGE(133),
        SPLIT_1_CHANGE(134),
        SPLIT_2_CHANGE(135),
        SPLIT_3_CHANGE(136),
        SPLIT_4_CHANGE(137),
        SPLIT_5_CHANGE(138),
        SPLIT_6_CHANGE(139),
        SPLIT_7_CHANGE(140),
        SPLIT_8_CHANGE(141),
        SPLIT_9_CHANGE(142),
        SPLIT_10_CHANGE(143),
        SPLIT_11_CHANGE(144),
        SPLIT_12_CHANGE(145),
        SPLIT_13_CHANGE(146),
        SPLIT_14_CHANGE(147),
        SPLIT_15_CHANGE(148),
        SPLIT_16_CHANGE(149),
        COORD_CYCLE_STATE_CHANGE(150),
        COORD_PHASE_YIELD_POINT(151),

        /**
         * Cabinet / System Events
         */
        TEST_INPUT_ON(171),
        TEST_INPUT_OFF(172),
        UNIT_FLASH_STATUS_CHANGE(173),
        UNIT_ALARM_STATUS_1_CHANGE(174),
        ALARM_GROUP_STATE_CHANGE(175),
        SPECIAL_FUNCTION_OUTPUT_ON(176),
        SPECIAL_FUNCTION_OUTPUT_OFF(177),
        MANUAL_CONTROL_ENABLE_OFF_ON(178),
        INTERVAL_ADVANCE_OFF_ON(179),
        STOP_TIME_INPUT_OFF_ON(180),
        CONTROLLER_CLOCK_UPDATED(181),
        POWER_FAILURE_DETECTED(182),
        POWER_RESTORED(184),
        VENDOR_SPECIFIC_ALARM(185);

        private int eventNum;

        Event(int eventNum) {
            this.eventNum = eventNum;
        }

        @JsonValue
        public int getEventNum() {
            return eventNum;
        }

        public static Event of(int eventNum) {
            return eventMap.get(eventNum);
        }

        /**
         * @return translated name depend on current language of default message service.
         * @apiNote Only call in spring context. See {@link BeanFinder#getDefaultMessageService()}
         */
        public String translatedName() {
            return BeanFinder.getDefaultMessageService().getMessage(this.name());
        }

    }

    /**
     * Reverse mapping from event number to event enum
     */
    private static final Map<Integer, Event> eventMap;

    static {
        var map = new HashMap<Integer, Event>();

        for (Event event : Event.values()) {
            map.put(event.getEventNum(), event);
        }

        eventMap = Collections.unmodifiableMap(map);
    }

    private static final long serialVersionUID = -7195140753899923908L;

    private LocalDateTime dateTime;

    private Event event;

    /**
     * Standard range of parameter is [0:255]. Siemens proprietary parameter is of long range.
     */
    private long parameter;

    private int ordinal;

    public PerfLogEventVO(LocalDateTime dateTime, Event event, long parameter) {
        this.dateTime = dateTime;
        this.event = event;
        this.parameter = parameter;
    }

    public PerfLogEventVO(LocalDateTime dateTime, Event event, long parameter, int ordinal) {
        this.dateTime = dateTime;
        this.event = event;
        this.parameter = parameter;
        this.ordinal = ordinal;
    }

    public static Map<Integer, Event> getEventMap() {
        return eventMap;
    }

    @JsonProperty("datetime")
    public LocalDateTime getDateTime() {
        return dateTime;
    }

    @JsonProperty("datetime")
    public void setDateTime(LocalDateTime dateTime) {
        this.dateTime = dateTime;
    }

    @JsonProperty("event")
    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    @JsonProperty("event")
    public void setEventNum(int eventNum) {
        this.event = eventMap.get(eventNum);
    }

    @JsonProperty("parameter")
    public long getParameter() {
        return parameter;
    }

    @JsonProperty("parameter")
    public void setParameter(long parameter) {
        this.parameter = parameter;
    }

    @JsonProperty("ordinal")
    public int getOrdinal() {
        return ordinal;
    }

    @JsonProperty("ordinal")
    public void setOrdinal(int ordinal) {
        this.ordinal = ordinal;
    }

    @JsonIgnore
    public boolean isPhaseEvent() {
        return Objects.nonNull(event)
                && event.getEventNum() >= Event.PHASE_ON.getEventNum()
                && event.getEventNum() <= Event.PHASE_INACTIVE.getEventNum();
    }

    @JsonIgnore
    public boolean isPedestrianEvent() {
        if (Objects.isNull(event)) {
            return false;
        }
        // NOTE: Include PHASE_ON event to calculate phase time
        if (event.getEventNum() == Event.PHASE_ON.getEventNum()) {
            return true;
        }

        return event.getEventNum() >= Event.PEDESTRIAN_BEGIN_WALK.getEventNum()
                && event.getEventNum() <= Event.PEDESTRIAN_DARK.getEventNum();
    }

    @JsonIgnore
    public boolean isPreemptionEvent() {
        return Objects.nonNull(event)
                && event.getEventNum() >= Event.PREEMPT_ADVANCE_WARNING_INPUT.getEventNum()
                && event.getEventNum() <= Event.PREEMPTION_BEGIN_EXIT_INTERVAL.getEventNum();
    }

    @JsonIgnore
    public boolean isPriorityEvent() {
        return Objects.nonNull(event)
                && event.getEventNum() >= Event.TSP_CHECK_IN.getEventNum()
                && event.getEventNum() <= Event.TSP_CHECK_OUT.getEventNum();
    }

    @JsonIgnore
    public boolean isGreenSignal() {
        return Event.PHASE_BEGIN_GREEN.equals(event);
    }

    @JsonIgnore
    public boolean isYellowSignal() {
        return Event.PHASE_BEGIN_YELLOW_CLEARANCE.equals(event);
    }

    @JsonIgnore
    public boolean isRedSignal() {
        return Event.PHASE_BEGIN_RED_CLEARANCE.equals(event);
    }

    @JsonIgnore
    public boolean isColorChangeSignal() {
        return isGreenSignal() || isRedSignal() || isYellowSignal();
    }

    @Override
    public int compareTo(PerfLogEventVO o) {
        int compareDateTime = this.dateTime.compareTo(o.dateTime);
        if (compareDateTime != 0) {
            return compareDateTime;
        }
        return Integer.compare(this.ordinal, o.ordinal);
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof PerfLogEventVO that)) {
            return false;
        }
        return parameter == that.parameter && ordinal == that.ordinal && Objects.equals(dateTime,
                that.dateTime) && event == that.event;
    }

    @Override
    public int hashCode() {
        return Objects.hash(dateTime, event, parameter, ordinal);
    }

}
